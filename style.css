/* -------------------------
   Fonts
-------------------------- */
@font-face {
  font-family: segoBd;
  src: url(./assets/fonts/segoe-ui-this/segoeuithibd.ttf);
}
@font-face {
  font-family: segoIs;
  src: url(./assets/fonts/segoe-ui-this/segoeuithis.ttf);
}

/* -------------------------
   Global Reset
-------------------------- */
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  cursor: default; /* Default cursor globally */
}

html,
body {
  height: 100%;
  width: 100%;
  font-family: segoIs;
}

:root {
  --brightness-level: 1;
}

body {
  filter: brightness(var(--brightness-level));
  transition: filter 0.3s ease-in-out;
}
/* -------------------------
   Main Layout
-------------------------- */
main {
  height: 100%;
  width: 100%;
}

/* -------------------------
   Desktop Background
-------------------------- */
.desktop {
  height: 100%;
  width: 100%;
  background-image: url("https://images.unsplash.com/photo-1697143493170-8cf836596b34?q=80&w=1632&auto=format&fit=crop");
  background-position: bottom;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
}

.desktopIcons {
  position: absolute;
  top: 3%;
  left: 1.5%;
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
}

.iconCtn {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 5rem;
  cursor: pointer;
  color: white;
  text-shadow: 1px 1px 2px black;
}

.iconCtn img {
  width: 3rem;
  height: 3rem;
  object-fit: contain;
}

.iconCtn h4 {
  margin-top: 4px;
  font-size: 0.8rem;
  text-align: center;
}

/* -------------------------
   Taskbar Styling
-------------------------- */
.taskBar {
  position: absolute;
  bottom: 1px;
  background: #e4eff9;
  width: 100%;
  height: 6.6vh;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.2rem 1rem;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
}

/* -------------------------
   Left Side of Taskbar (Weather)
-------------------------- */
.taskbarLeft {
  display: flex;
  align-items: center;
  gap: 10px;
  border-radius: 6px;
  padding: 2px 6px;
  cursor: pointer;
}
.cloud-icon {
  width: 2.2rem;
}
.weather h5 {
  font-size: 0.8rem;
}
.taskbarLeft:hover {
  background: rgba(0, 0, 0, 0.1);
}

/* -------------------------
   Middle Section: Icons & Search
-------------------------- */
.taskbarMid {
  display: flex;
  align-items: center;
  margin-left: 6rem;
}
.taskIcons {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 10px;
}

/* Individual Icons */
.windowIcon,
.allIcons,
.searchIcon,
.wifiIcon,
.soundIcon,
.batteryIcon {
  transition: 0.2s ease-in-out;
  cursor: pointer;
}

/* Start Menu Icon */
.windowIcon {
  width: 2.6rem;
  padding: 8px;
  border-radius: 6px;
}

/* Other Application Icons */
.allIcons {
  width: 2.6rem;
  padding: 6px;
  border-radius: 8px;
}

/* Hover Effect Like Windows 11 */
.allIcons:hover,
.windowIcon:hover {
  background: rgba(0, 0, 0, 0.1);
}

/* Search Bar */
.searchBar {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 50px;
  width: 10rem;
  padding: 0.99vh 0.6rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}
.searchIcon {
  width: 1rem;
}
input {
  border: none;
  background: transparent;
  margin-left: 6px;
  font-size: 0.9rem;
  cursor: text;
}
input:focus {
  outline: none;
}

/* -------------------------
   Right Side: System Info
-------------------------- */
.taskbarRight {
  display: flex;
  align-items: center;
  justify-content: center;
}

.upArrow {
  font-size: 1.6rem;
  cursor: pointer;
  padding: 2px 3px;
  border-radius: 6px;
}
.upArrow:hover,
.Lang h5:hover {
  background: rgba(0, 0, 0, 0.1);
}

.Lang h5 {
  padding: 5px;
  border-radius: 6px;
  font-size: 0.8rem;
  text-align: center;
  cursor: pointer;
}

.controlIcons {
  display: flex;
  align-items: center;
  border-radius: 6px;
}
.controlIcons img {
  width: 1.67rem;
  padding: 4px;
  cursor: pointer;
}
.controlIcons .batteryIcon {
  width: 1.9rem;
}
.controlIcons:hover {
  background: rgba(0, 0, 0, 0.1);
}

.dateTimeCtn {
  text-align: right;
  padding: 3px 6px;
  border-radius: 6px;
  cursor: pointer;
}
.dateTimeCtn h4 {
  font-size: 0.86rem;
}
.dateTimeCtn:hover {
  background: rgba(0, 0, 0, 0.1);
}

/* -------------------------
   Start Window UI
-------------------------- */
.startWindow {
  position: absolute;
  bottom: 8%;
  left: 50%;
  transform: translate(-50%);
  background: #e4eff9;
  height: 80%;
  width: 50%;
  border-radius: 10px;
  z-index: 999;
}

.pinnedCtn {
  height: 45%;
  padding: 1.1rem 2.2rem;
  width: 100%;
}
.pinBar {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.pinBar h3 {
  font-size: 0.9rem;
  font-weight: 600;
}

.allAppsIcon {
  padding: 4px 6px;
  border-radius: 6px;
  background: rgba(0, 0, 0, 0.1);
  cursor: pointer;
}
.allAppsIcon h3 {
  font-size: 0.8rem;
  cursor: pointer;
}
.allAppsIcon h3 i {
  margin-left: 3px;
  cursor: pointer;
}

.appsGrid {
  margin-top: 12px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 2.5rem;
  text-align: center;
  flex-wrap: wrap;
}
.appCtn {
  border-radius: 6px;
  padding: 4px 10px;
  cursor: pointer;
}
.appCtn:hover {
  background: rgba(0, 0, 0, 0.1);
}
.appCtn img {
  cursor: pointer;

  width: 3rem;
}
.appCtn h5 {
  font-size: 0.9rem;
}

.recommendedCtn {
  height: 45%;
  width: 100%;
  padding: 1.1rem 2.2rem;
}
.RecCtn {
  display: flex;
  align-items: center;
  margin-top: 15px;
  justify-content: start;
  flex-wrap: wrap;
}
.fileCtn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1vh;
  padding-right: 8rem;
  gap: 12px;
  border-radius: 6px;
  cursor: pointer;
}
.fileCtn:nth-child(even) {
  margin-left: 8vh;
}
.fileCtn:hover {
  background: rgba(0, 0, 0, 0.1);
}
.fileimg img {
  width: 2.6rem;
}
.fileTxt h4 {
  font-size: 1rem;
}
.fileTxt h5 {
  opacity: 0.7;
}

.profileBar {
  height: 10%;
  width: 100%;
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.1);
  justify-content: space-between;
  border-radius: 6px;
  padding: 1.1rem 2.2rem;
}
.prf {
  display: flex;
  align-items: center;
  gap: 10px;
}
.prf h2 {
  font-size: 1.1rem;
}
.prf img {
  width: 2rem;
  border-radius: 50px;
}

.powerIcon img {
  width: 1.4rem;
  cursor: pointer;
  transition: transform 0.2s ease-in-out;
}
.powerIcon img:active {
  transform: scale(0.7); /* Fix: add transform effect */
}

/* -------------------------
   App Window Style
-------------------------- */
.appWindow {
  position: absolute;
  top: 10%;
  left: 20%;
  width: 40%;
  height: 40%;
  background: #ffffff;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  display: none;
  flex-direction: column;
  z-index: 10;
  cursor: pointer;
}

.appHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #0067c0;
  color: white;
  padding: 0.5rem 1rem;
  font-weight: bold;
  user-select: none;
  cursor: move;
  cursor: pointer;
}

.appTitle {
  font-size: 1rem;
}

.windowControls button {
  margin-left: 5px;
  font-size: 1.3rem;
  padding: 4px 9px;
  border: none;
  background: none;
  color: white;
  cursor: pointer;
  border-radius: 6px;
  font-weight: bold;
  transition: 0.2s ease-in-out;
}
.windowControls button:hover {
  background: rgba(255, 255, 255, 0.2);
}
.windowControls button:nth-child(3):hover {
  background-color: red;
}

.appContent {
  padding: 1rem;
  font-size: 0.9rem;
}

.desktopIcons {
  position: absolute;
  top: 3%;
  left: 1.5%;
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
}

.iconCtn {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 5rem;
  cursor: pointer;
  color: white;
  text-shadow: 1px 1px 2px black;
}

.iconCtn img {
  width: 3rem;
  height: 3rem;
  object-fit: contain;
}

.iconCtn h4 {
  margin-top: 4px;
  font-size: 0.8rem;
  text-align: center;
}

/* Quick Settings Panel - Windows 11 Style */
.glass-panel {
  background: rgba(32, 34, 40, 0.82);
  backdrop-filter: blur(22px) saturate(1.7);
  border-radius: 18px;
  box-shadow: 0 8px 32px #0007, 0 1.5px 0 #fff2 inset;
  border: 1.5px solid rgba(255, 255, 255, 0.13);
  overflow: hidden;
}
.qs-media {
  display: flex;
  align-items: center;
  padding: 18px 18px 10px 18px;
  border-bottom: 1px solid #fff2;
  background: rgba(255, 255, 255, 0.03);
}
.qs-media img {
  box-shadow: 0 2px 8px #0002;
}
.qs-btn {
  background: none;
  border: none;
  color: #eaf6ff;
  font-size: 1.2em;
  margin: 0 2px;
  cursor: pointer;
  border-radius: 6px;
  padding: 6px 8px;
  transition: background 0.15s, color 0.15s;
}
.qs-btn:hover {
  background: #2a7fff22;
  color: #fff;
}
.qs-toggle {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(255, 255, 255, 0.09);
  border: none;
  border-radius: 12px;
  padding: 12px 0 6px 0;
  color: #eaf6ff;
  font-size: 0.98em;
  cursor: pointer;
  transition: background 0.18s, box-shadow 0.18s, color 0.18s;
  box-shadow: 0 2px 8px #0001;
  gap: 6px;
}
.qs-toggle.active,
.qs-toggle:active {
  background: #2a7fffcc;
  color: #fff;
  box-shadow: 0 4px 16px #2a7fff33;
}
.qs-toggle img {
  width: 28px;
  height: 28px;
  margin-bottom: 2px;
  filter: drop-shadow(0 1px 2px #0002);
}
.qs-toggles {
  user-select: none;
}
.qs-sliders input[type="range"] {
  accent-color: #2a7fff;
  width: 100%;
  margin: 0 8px;
  background: transparent;
}
.qs-sliders span,
.qs-sliders label {
  color: #eaf6ff;
}
.qs-sliders {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 0 0 18px 18px;
}
.qs-sliders .qs-btn {
  padding: 4px 8px;
  font-size: 1.2em;
}
.qs-sliders .qs-btn i {
  font-size: 1.3em;
}
.qs-sliders .qs-btn:active {
  background: #2a7fff33;
}
.qs-sliders .qs-btn:focus {
  outline: 2px solid #2a7fff;
}
.qs-sliders .qs-btn {
  color: #eaf6ff;
}
.qs-sliders .qs-btn:hover {
  color: #fff;
}
.qs-sliders .qs-btn:active {
  color: #fff;
}
.qs-sliders .qs-btn:focus {
  color: #fff;
}
.qs-sliders .qs-btn {
  background: none;
  border: none;
}
.qs-sliders .qs-btn i {
  font-size: 1.3em;
}
.qs-sliders .qs-btn {
  padding: 0 6px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.qs-sliders .qs-btn {
  color: #eaf6ff;
}
.qs-sliders .qs-btn:hover {
  color: #fff;
}
.qs-sliders .qs-btn:active {
  color: #fff;
}
.qs-sliders .qs-btn:focus {
  color: #fff;
}
.qs-sliders .qs-btn {
  background: none;
  border: none;
}
.qs-sliders .qs-btn i {
  font-size: 1.3em;
}
.qs-sliders .qs-btn {
  padding: 0 6px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.qs-sliders .qs-btn {
  color: #eaf6ff;
}
.qs-sliders .qs-btn:hover {
  color: #fff;
}
.qs-sliders .qs-btn:active {
  color: #fff;
}
.qs-sliders .qs-btn:focus {
  color: #fff;
}
.qs-sliders .qs-btn {
  background: none;
  border: none;
}
.qs-sliders .qs-btn i {
  font-size: 1.3em;
}
.qs-sliders .qs-btn {
  padding: 0 6px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.qs-sliders .qs-btn {
  color: #eaf6ff;
}
.qs-sliders .qs-btn:hover {
  color: #fff;
}
.qs-sliders .qs-btn:active {
  color: #fff;
}
.qs-sliders .qs-btn:focus {
  color: #fff;
}
@media (max-width: 500px) {
  #quickSettingsPanel {
    width: 98vw !important;
    right: 1vw !important;
    left: 1vw !important;
    min-width: unset;
    max-width: unset;
  }
}

/* Ensure pointer cursor for all interactive elements */
button,
.qs-btn,
.qs-toggle,
input[type="range"],
.controlIcons,
.taskIcons img,
.windowIcon,
.allIcons,
.searchIcon,
.taskbarLeft,
.Lang h5,
.upArrow,
.dateTimeCtn,
.appCtn,
.fileCtn,
.powerIcon img,
.profileBar,
#startBtn {
  cursor: pointer !important;
}

/* Remove pointer from non-interactive elements if inherited */
.desktop,
.desktopIcons,
.iconCtn h4,
.fileTxt h4,
.fileTxt h5,
.weather h5,
.startWindow,
.pinnedCtn,
.recommendedCtn,
.appsGrid,
.RecCtn {
  cursor: default !important;
}
